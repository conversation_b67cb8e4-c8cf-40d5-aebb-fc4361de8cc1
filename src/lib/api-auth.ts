import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, JWTPayload } from '@/lib/auth';

export interface AuthContext {
   user: JWTPayload;
}

export interface AuthResult {
   success: boolean;
   user?: JWTPayload;
   error?: string;
}

/**
 * Validates API request authentication from Authorization header
 */
export async function validateApiRequest(request: NextRequest): Promise<AuthResult> {
   try {
      const authHeader = request.headers.get('authorization');

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
         return {
            success: false,
            error: 'Authentication required',
         };
      }

      const accessToken = authHeader.substring(7); // Remove 'Bearer ' prefix
      const tokenPayload = verifyToken(accessToken);

      if (!tokenPayload) {
         return {
            success: false,
            error: 'Invalid or expired token',
         };
      }

      return {
         success: true,
         user: tokenPayload,
      };
   } catch (error) {
      console.error('API authentication error:', error);
      return {
         success: false,
         error: 'Authentication failed',
      };
   }
}

/**
 * Higher-order function that wraps API route handlers with authentication
 * Requires authenticated and verified users only
 */
export function withAuth(
   handler: (request: NextRequest, context: AuthContext, routeContext?: any) => Promise<Response>
) {
   return async (request: NextRequest, routeContext?: any): Promise<Response> => {
      try {
         const authResult = await validateApiRequest(request);

         if (!authResult.success) {
            return NextResponse.json({ error: authResult.error }, { status: 401 });
         }

         const user = authResult.user!;

         // Require verified account for all protected routes
         if (!user.verified) {
            return NextResponse.json({ error: 'Account not verified' }, { status: 403 });
         }

         // Call the wrapped handler with authenticated user context
         return await handler(request, { user }, routeContext);
      } catch (error) {
         console.error('Auth wrapper error:', error);
         return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
      }
   };
}

/**
 * Utility function to create unauthorized response
 */
export function createUnauthorizedResponse(message = 'Authentication required') {
   return NextResponse.json({ error: message }, { status: 401 });
}

/**
 * Utility function to create forbidden response
 */
export function createForbiddenResponse(message = 'Access forbidden') {
   return NextResponse.json({ error: message }, { status: 403 });
}
