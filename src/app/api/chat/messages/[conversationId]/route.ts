import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/api-auth';
import { getMessages } from '@/modules/chat/api/message-server-api';
import { getMessagesSchema } from '@/modules/chat/schemas';

export const GET = withAuth(
   async (
      request: NextRequest,
      { user },
      { params }: { params: Promise<{ conversationId: string }> }
   ) => {
      try {
         // Extract conversation ID from params
         const { conversationId } = await params;

         const { searchParams } = new URL(request.url);

         const queryParams = {
            conversation_id: conversationId,
            limit: parseInt(searchParams.get('limit') || '50'),
            offset: parseInt(searchParams.get('offset') || '0'),
            before_id: searchParams.get('before_id') || undefined,
         };

         const validation = getMessagesSchema.safeParse(queryParams);
         if (!validation.success) {
            return NextResponse.json(
               { error: 'Invalid parameters', details: validation.error.issues },
               { status: 400 }
            );
         }

         const { data, error } = await getMessages(
            conversationId,
            user.userId,
            validation.data.limit,
            validation.data.offset
         );

         if (error) {
            return NextResponse.json({ error }, { status: 500 });
         }

         return NextResponse.json(data);
      } catch (error) {
         console.error('Get messages error:', error);
         return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
      }
   }
);
