import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/api-auth';
import { getConversationDetails } from '@/modules/chat/api/conversation-server-api';

export const GET = withAuth(
   async (
      request: NextRequest,
      { user },
      { params }: { params: Promise<{ conversationId: string }> }
   ) => {
      try {
         // Extract conversation ID from params
         const { conversationId } = await params;

         if (!conversationId) {
            return NextResponse.json({ error: 'Conversation ID is required' }, { status: 400 });
         }

         const { data, error } = await getConversationDetails(conversationId, user.userId);

         if (error) {
            return NextResponse.json({ error }, { status: 500 });
         }

         return NextResponse.json(data);
      } catch (error) {
         console.error('Get conversation details error:', error);
         return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
      }
   }
);
