import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/api-auth';
import { getGroupChatBySubject } from '@/modules/subject/api/conversation-server-api';
import { subjectParamsSchema } from '@/modules/subject/schemas';

export const GET = withAuth(
   async (
      request: NextRequest,
      { user },
      { params }: { params: Promise<{ subjectId: string }> }
   ) => {
      try {
         // Extract subject ID from params
         const { subjectId } = await params;

         // Validate subject ID
         const validation = subjectParamsSchema.safeParse({ id: subjectId });
         if (!validation.success) {
            return NextResponse.json({ error: 'Invalid subject ID format' }, { status: 400 });
         }

         // Get or create group chat for subject
         const { data, error } = await getGroupChatBySubject(subjectId, user.userId);

         if (error) {
            return NextResponse.json({ error }, { status: 500 });
         }

         return NextResponse.json(data);
      } catch (error) {
         console.error('Group chat API error:', error);
         return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
      }
   }
);