import { Message, SendMessageRequest, ConversationDetails } from '../types';
import apiClient from '@/lib/axios';

export class ChatClient {
   static async getMessages(
      conversationId: string,
      limit = 50,
      offset = 0
   ): Promise<{ data: Message[] | null; error: string | null }> {
      try {
         const params = new URLSearchParams({
            limit: limit.toString(),
            offset: offset.toString(),
         });

         const response = await apiClient.get(`/chat/messages/${conversationId}?${params}`);
         return { data: response.data, error: null };
      } catch (error: any) {
         console.error('Error fetching messages:', error);
         return {
            data: null,
            error: error.response?.data?.error || 'Failed to fetch messages',
         };
      }
   }

   static async sendMessage(
      request: SendMessageRequest
   ): Promise<{ data: Message | null; error: string | null }> {
      try {
         const response = await apiClient.post('/chat/messages/send', request);
         return { data: response.data, error: null };
      } catch (error: any) {
         console.error('Error sending message:', error);
         return {
            data: null,
            error: error.response?.data?.error || 'Failed to send message',
         };
      }
   }

   static async getConversationDetails(
      conversationId: string
   ): Promise<{ data: ConversationDetails | null; error: string | null }> {
      try {
         const response = await apiClient.get(`/conversations/${conversationId}`);
         return { data: response.data, error: null };
      } catch (error: any) {
         console.error('Error fetching conversation:', error);
         return {
            data: null,
            error: error.response?.data?.error || 'Failed to fetch conversation',
         };
      }
   }
}
