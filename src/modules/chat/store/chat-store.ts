import { create } from 'zustand';
import { Message, ConversationDetails } from '../types';
import apiClient from '@/lib/axios';

interface ChatState {
   // State
   conversations: Record<string, ConversationDetails>;
   messages: Record<string, Message[]>;
   loading: Record<string, boolean>;
   error: string | null;
   currentConversationId: string | null;

   // Actions
   setCurrentConversation: (id: string) => void;
   loadMessages: (conversationId: string) => Promise<void>;
   addMessage: (conversationId: string, message: Message) => void;
   updateMessage: (conversationId: string, message: Message) => void;
   sendMessage: (
      conversationId: string,
      content: string,
      type?: 'text' | 'file' | 'image'
   ) => Promise<void>;
   clearError: () => void;
}

export const useChatStore = create<ChatState>(set => ({
   conversations: {},
   messages: {},
   loading: {},
   error: null,
   currentConversationId: null,

   setCurrentConversation: id => {
      set({ currentConversationId: id });
   },

   loadMessages: async conversationId => {
      set(state => ({
         loading: { ...state.loading, [conversationId]: true },
      }));

      try {
         const response = await apiClient.get(`/chat/messages/${conversationId}`);

         set(state => ({
            messages: { ...state.messages, [conversationId]: response.data || [] },
            loading: { ...state.loading, [conversationId]: false },
            error: null,
         }));
      } catch (error: any) {
         set(state => ({
            loading: { ...state.loading, [conversationId]: false },
            error: error.response?.data?.error || 'Failed to load messages',
         }));
      }
   },

   addMessage: (conversationId, message) => {
      set(state => ({
         messages: {
            ...state.messages,
            [conversationId]: [message, ...(state.messages[conversationId] || [])],
         },
      }));
   },

   updateMessage: (conversationId, updatedMessage) => {
      set(state => ({
         messages: {
            ...state.messages,
            [conversationId]: (state.messages[conversationId] || []).map(msg =>
               msg.id === updatedMessage.id ? updatedMessage : msg
            ),
         },
      }));
   },

   sendMessage: async (conversationId, content, type = 'text') => {
      try {
         await apiClient.post('/chat/messages/send', {
            conversation_id: conversationId,
            content,
            message_type: type,
         });

         // Message will be added via real-time subscription
      } catch (error: any) {
         set({ error: error.response?.data?.error || 'Failed to send message' });
      }
   },

   clearError: () => set({ error: null }),
}));


