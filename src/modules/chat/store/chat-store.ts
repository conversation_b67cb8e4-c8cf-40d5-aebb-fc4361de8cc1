import { create } from 'zustand';
import { Message, ConversationDetails } from '../types';

interface ChatState {
  // State
  conversations: Record<string, ConversationDetails>;
  messages: Record<string, Message[]>;
  loading: Record<string, boolean>;
  error: string | null;
  currentConversationId: string | null;

  // Actions
  setCurrentConversation: (id: string) => void;
  loadMessages: (conversationId: string) => Promise<void>;
  addMessage: (conversationId: string, message: Message) => void;
  updateMessage: (conversationId: string, message: Message) => void;
  sendMessage: (conversationId: string, content: string, type?: 'text' | 'file' | 'image') => Promise<void>;
  clearError: () => void;
}

export const useChatStore = create<ChatState>(set => ({
   conversations: {},
   messages: {},
   loading: {},
   error: null,
   currentConversationId: null,

   setCurrentConversation: id => {
      set({ currentConversationId: id });
   },

   loadMessages: async conversationId => {
      set(state => ({
         loading: { ...state.loading, [conversationId]: true },
      }));

      try {
         const response = await fetch(`/api/chat/messages/${conversationId}`, {
            credentials: 'include',
         });

         if (!response.ok) {
            const errorData = await response.json();
            set({ error: errorData.error || 'Failed to load messages' });
            return;
         }

         const data = await response.json();

         set(state => ({
            messages: { ...state.messages, [conversationId]: data || [] },
            loading: { ...state.loading, [conversationId]: false },
            error: null,
         }));
      } catch (error) {
         set(state => ({
            loading: { ...state.loading, [conversationId]: false },
            error: 'Failed to load messages',
         }));
      }
   },

   addMessage: (conversationId, message) => {
      set(state => ({
         messages: {
            ...state.messages,
            [conversationId]: [message, ...(state.messages[conversationId] || [])],
         },
      }));
   },

   updateMessage: (conversationId, updatedMessage) => {
      set(state => ({
         messages: {
            ...state.messages,
            [conversationId]: (state.messages[conversationId] || []).map(msg =>
               msg.id === updatedMessage.id ? updatedMessage : msg
            ),
         },
      }));
   },

   sendMessage: async (conversationId, content, type = 'text') => {
      try {
         const response = await fetch('/api/chat/messages/send', {
            method: 'POST',
            headers: {
               'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
               conversation_id: conversationId,
               content,
               message_type: type,
            }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            set({ error: errorData.error || 'Failed to send message' });
            return;
         }

         // Message will be added via real-time subscription
      } catch (error) {
         set({ error: 'Failed to send message' });
      }
   },

   clearError: () => set({ error: null }),
}));


