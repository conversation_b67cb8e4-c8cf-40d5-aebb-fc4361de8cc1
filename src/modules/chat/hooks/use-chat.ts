import { useEffect } from 'react';
import { useChatStore } from '../store';
import { useRealtimeMessages } from './use-realtime-messages';
import { useTypingIndicator } from './use-typing-indicator';

export function useChat(conversationId: string, userId: string) {
   // Get state using getState() to avoid re-render issues
   const state = useChatStore.getState();
   const messages = state.messages[conversationId] || [];
   const loading = state.loading[conversationId] || false;
   const error = state.error;

   // Direct action access
   const { loadMessages, sendMessage, setCurrentConversation, clearError } = state;

   // Real-time subscriptions
   const { isConnected } = useRealtimeMessages(conversationId);
   const { typingUsers, startTyping, stopTyping } = useTypingIndicator(conversationId, userId);

   // Load messages on mount
   useEffect(() => {
      if (conversationId && !messages.length && !loading) {
         loadMessages(conversationId);
      }
   }, [conversationId, messages.length, loading, loadMessages]);

   // Set current conversation
   useEffect(() => {
      setCurrentConversation(conversationId);
   }, [conversationId, setCurrentConversation]);

   return {
      messages,
      loading,
      error,
      isConnected,
      typingUsers,
      sendMessage: (content: string) => sendMessage(conversationId, content),
      startTyping,
      stopTyping,
      clearError,
   };
}
