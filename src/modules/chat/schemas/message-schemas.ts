import { z } from 'zod';
import { CHAT_CONFIG, MESSAGE_TYPES } from '../constants';

export const sendMessageSchema = z
   .object({
      conversation_id: z.string().min(1),
      content: z.string().min(1).max(2000).optional(),
      message_type: z.enum([MESSAGE_TYPES.TEXT, MESSAGE_TYPES.FILE, MESSAGE_TYPES.IMAGE]),
   })
   .refine(data => (data.message_type === MESSAGE_TYPES.TEXT ? !!data.content : true), {
      message: 'Text messages must have content',
   });

export const getMessagesSchema = z.object({
   conversation_id: z.string().min(1),
   limit: z.number().min(1).max(100).default(CHAT_CONFIG.MESSAGES_PER_PAGE),
   offset: z.number().min(0).default(0),
   before_id: z.string().min(1).optional(),
});

export const fileUploadSchema = z.object({
   file: z
      .instanceof(File)
      .refine(file => file.size <= CHAT_CONFIG.MAX_FILE_SIZE, 'File too large')
      .refine(file => CHAT_CONFIG.ALLOWED_FILE_TYPES.includes(file.type), 'Invalid file type'),
   conversation_id: z.string().min(1),
});
